'use client';
import React, {useEffect} from "react";
import {usePBContext} from "@/context/PocketbaseContext";
import {useRouter} from "next/navigation";
import {useReferral} from "@/hooks/use-referral";
import SharedLayout from "@/components/SharedLayout";

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedLayoutContent: React.FC<ProtectedRouteProps> = ({children}) => {
  // await connection();
  const {user} = usePBContext();
  const router = useRouter();

  useEffect(() => {
    if (!user) {
      router.replace('/login');
    }
  }, [user, router]);

  // import for effects
  const _ = useReferral();

  if (!user) {
    return (
      <SharedLayout>
        {children}
      </SharedLayout>
    );
  }

  return <>{children}</>;
};

export default ProtectedLayoutContent;
