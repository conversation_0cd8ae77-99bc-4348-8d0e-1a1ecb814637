import ClientProviders from "./ClientProviders";
import ProtectedLayoutContent from "./ProtectedLayoutContent";
import React, { Suspense } from "react";

const ProtectedLayout: React.FC<{children: React.ReactNode}> = ({children}) => {
  return (
      <ClientProviders>
        <Suspense fallback={null}>
          <ProtectedLayoutContent>
            {children}
          </ProtectedLayoutContent>
        </Suspense>
      </ClientProviders>
  )
}


export default ProtectedLayout;
