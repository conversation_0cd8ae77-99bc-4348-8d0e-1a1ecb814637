import type { Metadata } from 'next';
import LandingSMM from '@/screens/microLandings/SMM';

export const metadata: Metadata = {
  title: 'Создание контента для SMM с помощью ИИ - Холст.ИИ',
  description: 'Упростите и ускорьте процесс создания контента для постов в социальных сетях. Создавайте яркий визуал, вдохновляющий и привлекающий внимание.',
  keywords: 'SMM контент, социальные сети, визуал для соцсетей, контент маркетинг, ИИ для SMM, создание постов',
  openGraph: {
    title: 'Создание контента для SMM с помощью ИИ - Холст.ИИ',
    description: 'Упростите и ускорьте процесс создания контента для постов в социальных сетях. Создавайте яркий визуал, вдохновляющий и привлекающий внимание.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Создание контента для SMM с помощью ИИ - Холст.ИИ',
    description: 'Упростите и ускорьте процесс создания контента для постов в социальных сетях. Создавайте яркий визуал, вдохновляющий и привлекающий внимание.',
  },
};

export default function SMMPage() {
  return <LandingSMM />;
}
