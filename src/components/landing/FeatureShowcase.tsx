import { cn } from '@/lib/utils';
import Link from "next/link";

interface ImageElement {
  type: string;
  subject?: string;
  position: string;
  content?: any;
  element?: string; // Add this line to fix the TypeScript error
}

interface FeatureShowcaseProps {
  title: string;
  description: string;
  image: {
    type: string;
    elements: ImageElement[];
  };
  landingLink?: string;
  reverse?: boolean;
  className?: string;
}

export function FeatureShowcase({
  title,
  description,
  image,
  landingLink,
  reverse = false,
  className,
}: FeatureShowcaseProps) {
  const mainImage = image.elements.find(el => el.position === 'main');

  return (
    <div className={cn('w-full py-10 lg:py-10', className)}>
      <div
        className={cn(
          'w-full flex flex-col lg:flex-row items-center gap-8 lg:gap-12 rounded-2xl lg:rounded-2xl shadow-2xl border border-gray-200 px-8 py-8',
          reverse && 'lg:flex-row-reverse'
        )}
      >
        {/* Text Content */}
        <div
          className={cn(
            'w-full lg:w-1/2 space-y-6',
            // Remove padding if we want flush alignment
            !reverse ? 'lg:pr-0' : 'lg:pl-0'
          )}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-text-dark">
            {title}
          </h2>
          <p className="text-lg text-text-light leading-relaxed">
            {description}
          </p>
          {/* CTA Button - Only visible on desktop */}
          <div className="mb-8 hidden lg:flex lg:flex-col lg:items-start">
            <Link
              href={'/studio'}
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              Начать творить
            </Link>
            {landingLink && (
              <Link
                href={landingLink}
                className="mt-2 text-md text-gray-500 underline decoration-blue-600 underline-offset-4 hover:text-gray-700 hover:decoration-blue-700"
              >
                Узнать больше
              </Link>
            )}
          </div>
        </div>

        {/* Image */}
        <div
          className={cn(
            'aspect-square relative overflow-hidden w-full max-w-[400px]',
            !reverse ? 'lg:ml-auto' : 'lg:mr-auto'
          )}
        >
          <img
            src={`/samples/${mainImage?.subject}`}
            alt={mainImage?.subject || 'AI Generated Image'}
            className="w-full h-full object-contain"
          />
        </div>

        {/* CTA Button - Only visible on mobile, positioned after image */}
        <div className="w-full lg:hidden">
          <Link
            href={'/studio'}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            Начать творить
          </Link>
          {landingLink && (
            <Link
              href={landingLink}
              className="mt-4 inline-flex text-md text-gray-500 underline decoration-blue-600 underline-offset-4 hover:text-gray-700 hover:decoration-blue-700"
            >
              Узнать больше
            </Link>
          )}
        </div>
      </div>
    </div>
  );
}
