@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    /* Primary Colors */
    --primary-blue: #4285f4;
    --primary-blue-hover: #3367d6;
    --primary-blue-light: #6fa8ff;

    /* Background Colors */
    --dark-primary: #0f0f23;
    --dark-secondary: #1a1a2e;
    --dark-tertiary: #252547;

    /* Text Colors */
    --text-white: #ffffff;
    --text-gray: #a0a0a0;
    --text-gray-light: #d0d0d0;

    /* Accent Colors */
    --accent-purple: #6c5ce7;
    --accent-cyan: #00cec9;
    --accent-orange: #ff7675;

    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Colors */
    --brand-purple: #8586f1;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #2b65ea 100%);
    --accent-blue: #3b82f6;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --background-primary: #ffffff;
    --background-secondary: #f9fafb;
    --border-light: #e5e7eb;
    --border-focus: #349ff6;
    --preview-bg: #f8fafc;

    /* Typography */
    --heading-weight-semibold: 600;
    --heading-weight-bold: 700;
    --heading-weight-extrabold: 800;
    --body-weight: 400;
    --line-height-headings: 1.2;
    --line-height-body: 1.6;
    --line-height-captions: 1.4;
    --size-hero-heading: clamp(2rem, 5vw, 3.5rem);
    --size-description: 1.125rem;

    /* Layout */
    --container-max-width: 1200px;
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;
    --border-radius-sm: 8px;
    --border-radius-md: 12px;

    /* Header */
    --header-background: rgba(255, 255, 255, 0.95);
    --header-backdrop-filter: blur(10px);
    --header-height: 64px;

    /* Footer */
    --footer-background: #1f2937;
    --footer-color: #ffffff;
    --footer-padding: 48px 0 24px;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: var(--text-dark);
    line-height: var(--line-height-body);
  }

  h1, h2, h3, h4, h5, h6 {
    line-height: var(--line-height-headings);
    font-weight: var(--heading-weight-bold);
  }
}

@layer components {
  /* Buttons */
  .btn-primary {
    background: var(--gradient-primary);
    color: #ffffff;
    border-radius: var(--border-radius-sm);
    padding: 12px 24px;
    font-weight: var(--heading-weight-semibold);
    transition: all 0.2s ease;
  }
  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px blue;
  }

  .btn-secondary {
    background: var(--background-primary);
    color: #374151;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-sm);
    padding: 12px 24px;
    transition: all 0.2s ease;
  }
  .btn-secondary:hover {
    border-color: #d1d5db;
    background: var(--background-secondary);
  }

  .btn-cta-primary {
    background: var(--gradient-primary);
    color: #ffffff;
    padding: 12px 32px;
    border-radius: var(--border-radius-sm);
    font-weight: var(--heading-weight-semibold);
  }

  .btn-cta-secondary {
    background: transparent;
    color: var(--brand-purple);
    border: 2px solid var(--brand-purple);
    padding: 10px 30px;
    border-radius: var(--border-radius-sm);
  }

  /* Cards */
  .card {
    background: var(--background-primary);
    border-radius: var(--border-radius-md);
    padding: 24px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
  }
  .card:hover {
    transform: scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Inputs */
  .textarea-input {
    background: var(--background-primary);
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius-md);
    padding: 16px;
    min-height: 120px;
    transition: all 0.2s ease;
  }
  .textarea-input:focus {
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
  }

  .standard-input {
    border-radius: var(--border-radius-sm);
    padding: 12px 16px;
    border: 2px solid var(--border-light);
    transition: all 0.2s ease;
  }
  .standard-input:focus {
    border-color: var(--border-focus);
    outline: none;
  }
}

@layer utilities {
  .container {
    max-width: var(--container-max-width);
    margin-left: auto;
    margin-right: auto;
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
  }

  /* Effects */
  .hover-lift {
    transition: all 0.2s ease;
  }
  .hover-lift:hover {
    transform: translateY(-2px);
  }

  .focus-glow:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  }

  .image-grid {
    display: grid;
    gap: var(--spacing-md);
  }

  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #31a0fa 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .gradient-tertiary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .gradient-hero {
    background: linear-gradient(135deg, #667eea 0%, #4cc7fb 50%, #b6ddfa 100%);
  }

  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #45a6f4 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-blue {
    background: linear-gradient(135deg, #4285f4 0%, #6fa8ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .glass-card {
    background: rgba(26, 26, 46, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  .animate-confetti {
    animation: confetti-fall 3s ease-out forwards;
  }

  .animate-bounce-in {
    animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  .animate-pulse-success {
    animation: pulse-success 2s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes glow {
  from { box-shadow: 0 0 20px rgba(145, 196, 255, 0.3); }
  to { box-shadow: 0 0 30px rgba(66, 133, 244, 0.6); }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out;
}

@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(720deg);
    opacity: 0;
  }
}

@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse-success {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
}


.logo {
    height: 6em;
    padding: 1.5em;
    will-change: filter;
    transition: filter 300ms;
}
.logo:hover {
    filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
    filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@media (prefers-reduced-motion: no-preference) {
    a:nth-of-type(2) .logo {
        animation: logo-spin infinite 20s linear;
    }
}
