import { ModelInputConfig, CommonParameters, CommonGroups } from './types';

export const qwenImageConfig: ModelInputConfig = {
  modelId: 'qwen-image',
  modelName: 'Qwen Image',
  description: 'Модель генерации изображений с продвинутыми возможностями рендеринга текста и точного редактирования изображений',

  parameters: {
    // Aspect ratio
    aspect_ratio: {
      type: 'select',
      label: 'Соотношение сторон',
      description: 'Соотношение сторон сгенерированного изображения',
      defaultValue: '16:9',
      options: [
        { value: '1:1', label: '1:1 (Квадрат)' },
        { value: '16:9', label: '16:9 (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)' },
        { value: '9:16', label: '9:16 (М<PERSON><PERSON><PERSON><PERSON><PERSON>ный портрет)' },
        { value: '4:3', label: '4:3 (Стандартный)' },
        { value: '3:4', label: '3:4 (Портрет)' },
        { value: '3:2', label: '3:2 (Фото)' },
        { value: '2:3', label: '2:3 (Фотопортрет)' }
      ],
      order: 1,
      group: 'advanced'
    },

    // Image size
    image_size: {
      type: 'select',
      label: 'Размер изображения',
      description: 'Размер изображения для генерируемого изображения',
      defaultValue: 'optimize_for_quality',
      options: [
        { value: 'optimize_for_quality', label: 'Оптимизировать для качества' },
        { value: 'optimize_for_speed', label: 'Оптимизировать для скорости' }
      ],
      order: 2,
      group: 'advanced'
    },

    // Enhanced prompt
    enhance_prompt: {
      type: 'boolean',
      label: 'Улучшить запрос',
      description: 'Использовать ИИ для улучшения вашего запроса, чтобы получить лучший результат',
      defaultValue: false,
      order: 3,
      group: 'advanced'
    },

    // Negative prompt
    negative_prompt: {
      type: 'textarea',
      label: 'Негативный запрос',
      description: 'Описание того, чего следует избегать',
      defaultValue: '',
      placeholder: 'например, размытый, низкое качество, искаженный...',
      order: 4,
      group: 'advanced'
    },

    // Go fast
    go_fast: {
      type: 'boolean',
      label: 'Быстрый режим',
      description: 'Запустить более быструю генерацию с дополнительными оптимизациями',
      defaultValue: false,
      order: 5,
      group: 'advanced'
    },

    // Number of inference steps
    num_inference_steps: {
      type: 'slider',
      label: 'Шаги шумоподавления',
      description: 'Количество шагов шумоподавления. Рекомендуемый диапазон 28-50, меньшее количество шагов даёт более низкое качество, но быстрее',
      defaultValue: 28,
      min: 1,
      max: 50,
      step: 1,
      order: 6,
      group: 'advanced'
    },

    // Guidance
    guidance: {
      type: 'slider',
      label: 'Руководство',
      description: 'Руководство для сгенерированного изображения. Меньшие значения могут давать более реалистичные изображения. Хорошие значения: 2, 2.5, 3, 3.5',
      defaultValue: 3.0,
      min: 2.0,
      max: 4.0,
      step: 0.5,
      order: 7,
      group: 'advanced'
    },

    // Seed for reproducibility
    seed: {
      ...CommonParameters.seed,
      description: 'Случайное начальное значение. Устанавливается для воспроизводимой генерации',
      order: 8,
      group: 'advanced'
    },

    // Output format
    output_format: {
      type: 'select',
      label: 'Формат вывода',
      description: 'Формат выходных изображений',
      defaultValue: 'webp',
      options: [
        { value: 'webp', label: 'WebP (Рекомендуется)' },
        { value: 'jpg', label: 'JPEG' },
        { value: 'png', label: 'PNG' }
      ],
      order: 9,
      group: 'advanced'
    },

    // Output quality
    output_quality: {
      type: 'slider',
      label: 'Качество вывода',
      description: 'Качество при сохранении выходных изображений, от 0 до 100. 100 - лучшее качество, 0 - самое низкое. Не относится к PNG выходам',
      defaultValue: 80,
      min: 0,
      max: 100,
      step: 10,
      order: 10,
      group: 'advanced'
    },

    // Disable safety checker
    disable_safety_checker: {
      type: 'boolean',
      label: 'Отключить проверку безопасности',
      description: 'Отключить проверку безопасности для сгенерированных изображений',
      defaultValue: false,
      order: 11,
      group: 'advanced'
    },

    // LoRA weights
    lora_weights: {
      type: 'text',
      label: 'LoRA веса',
      description: 'Загрузить LoRA веса. Поддерживает модели Replicate в формате <owner>/<username>, URL HuggingFace в формате huggingface.co/<owner>/<model-name>, URL CivitAI или произвольные .safetensors URL',
      defaultValue: '',
      placeholder: 'например, \'fofr/flux-pixar-cars\'',
      order: 12,
      group: 'lora'
    },

    // LoRA scale
    lora_scale: {
      type: 'slider',
      label: 'Масштаб LoRA',
      description: 'Определяет, насколько сильно должна применяться основная LoRA. Разумные результаты между 0 и 1 для базового инференса',
      defaultValue: 1.0,
      min: 0,
      max: 2,
      step: 0.1,
      order: 13,
      group: 'lora'
    }
  },

  groups: {
    advanced: {
      ...CommonGroups.advanced,
      label: 'Расширенные настройки'
    },
    lora: {
      name: 'lora',
      label: 'LoRA Настройки',
      description: 'Настройки для загрузки и применения LoRA адаптеров',
      order: 2,
      collapsible: true,
      defaultExpanded: false
    }
  },

  metadata: {
    type: 'image',
    maxOutputs: 1,
    supportedFormats: ['webp', 'jpg', 'png'],
    maxResolution: '2K',
    estimatedTime: '5-30 seconds'
  }
};
