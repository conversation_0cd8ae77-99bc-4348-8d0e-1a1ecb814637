import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';

const features = [
    {
        title: 'Персонализация внешности и стиля',
        description: 'Выбирайте внешность, причёску, цвет волос, стиль одежды и аксессуары — ИИ создаёт уникальный визуальный образ инфлюэнсера.',
        image: 'influencer/1.webp',
        reverse: false
    },
    {
        title: 'Генерация различных эмоций и выражений лица',
        description: 'ИИ создаёт серию изображений с разными эмоциями: радость, удивление, задумчивость, чтобы контент выглядел живым и динамичным.',
        image: 'influencer/2.webp',
        reverse: true
    },
    {
        title: 'Анимация и видео для соцсетей',
        description: 'Ваш инфлюэнсер оживает: короткие видео, Reels и Stories с движениями и мимикой, готовые для публикации.',
        image: 'influencer/3.webp',
        reverse: false
    },
    {
        title: 'Смена локаций и фонов',
        description: 'ИИ автоматически подбирает фоны и сцены для фотографий и видео — студия, улица, интерьер или тематическая сцена под контент.',
        image: 'influencer/4.webp',
        reverse: true
    },
    {
        title: 'Создание брендированного контента',
        description: 'Инфлюэнсер может появляться с элементами бренда: логотип, фирменные цвета, продукты — идеально для рекламы и коллабораций.',
        image: 'influencer/5.webp',
        reverse: false
    }
]

const LandingInfluencer = () => {
    return (
        <div className="min-h-screen bg-background-primary">
            <Header />
            <Hero title="Создайте" titleHighlight="Виртуального инфлюэнсера за минуты" subtitle="Придумайте идеального цифрового героя для соцсетей. Вы задаёте внешний вид, стиль и характер — ИИ создаёт уникального инфлюэнсера, готового вести аккаунты, публиковать посты и взаимодействовать с аудиторией. Идеально для Instagram, TikTok, YouTube или других платформ." secondaryImage='/heroImages/influencer/landingHero2.jpg' topSecondaryImage='/heroImages/influencer/landingHero3.jpg' bottomSecondaryImage='/heroImages/influencer/landingHero4.jpg'/>
            <Features features={features}/>
            <FAQ />
            <Footer />
        </div>
    );
};

export default LandingInfluencer;
