import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';

const features = [
    {
        title: 'Анимация объектов на фото',
        description: 'ИИ превращает статичные элементы в двигающиеся — например, колышущиеся волосы, листья на дереве или мерцающий свет.',
        image: 'livingPhoto/1.webp',
        reverse: false
    },
    {
        title: 'Создание движущейся атмосферы',
        description: 'Добавление эффекта дождя, снега, тумана или света, чтобы фото выглядело живым и атмосферным.',
        image: 'livingPhoto/2.webp',
        reverse: true
    },
    {
        title: 'Оживление людей и персонажей',
        description: 'ИИ умеет анимировать лица, создавая лёгкие мимические движения, улыбки или взгляд в камеру, не теряя естественности.',
        image: 'livingPhoto/3.webp',
        reverse: false
    },
    {
        title: 'Превращение фото в короткое видео',
        description: 'Вы загружаете изображение — ИИ создаёт короткий клип с плавной анимацией элементов, готовый для публикации в Reels, TikTok или Stories.',
        image: 'livingPhoto/4.webp',
        reverse: true
    },
    {
        title: 'Стилизация движений под эмоции',
        description: 'Выбираете настроение: радость, динамику, спокойствие — ИИ подбирает стиль анимации, который усиливает эмоциональное восприятие фото.',
        image: 'livingPhoto/5.webp',
        reverse: false
    }
]

const LandingLivingPhoto = () => {
    return (
        <div className="min-h-screen bg-background-primary">
            <Header />
            <Hero title="Оживите свои фотографии" titleHighlight="C помощью ИИ" subtitle="Превратите обычные изображения в динамичные и живые визуалы. ИИ добавляет движение, эмоции и атмосферу, создавая анимации, которые цепляют взгляд. Идеально подходит для соцсетей, презентаций и рекламы — оживите свои фото без сложных программ и студий." secondaryImage='/heroImages/livePhoto/landingHero2.jpg' topSecondaryImage='/heroImages/livePhoto/landingHero3.jpg' bottomSecondaryImage='/heroImages/livePhoto/landingHero4.jpg'/>
            <Features features={features}/>
            <FAQ />
            <Footer />
        </div>
    );
};

export default LandingLivingPhoto;
